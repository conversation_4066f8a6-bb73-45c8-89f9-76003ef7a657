package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.ICartService;
import sarl.eazycar.items.domain.service.IContractService;
import sarl.eazycar.items.domain.service.IInvoiceService;
import sarl.eazycar.items.domain.service.IOrderService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour CheckoutService
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CheckoutServiceTest {

    @Mock
    private ICartService cartService;

    @Mock
    private IOrderService orderService;

    @Mock
    private IInvoiceService invoiceService;

    @Mock
    private IContractService contractService;

    @Mock
    private IArticleService articleService;

    @InjectMocks
    private CheckoutService checkoutService;

    private Cart activeCart;
    private Purchase testOrder;
    private Article testArticle;
    private Invoice testInvoice;
    private Contract testContract;

    @BeforeEach
    void setUp() {
        // Créer un article de test
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .available(true)
                .build();

        // Créer une commande de test
        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setArticle(testArticle);
        testOrder.setOrderDate(LocalDateTime.now());
        testOrder.setStatus(OrderStatus.IN_CART);
        testOrder.setShippingAddress("123 Test Street");

        // Créer un panier actif de test
        activeCart = Cart.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.valueOf(100.00))
                .orders(new ArrayList<>(List.of(testOrder)))
                .build();

        // Créer une facture de test
        testInvoice = Invoice.builder()
                .invoiceId("invoice-123")
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30))
                .status(InvoiceStatus.DRAFT)
                .order(testOrder)
                .build();

        // Créer un contrat de test
        testContract = Contract.builder()
                .contractId("contract-123")
                .documentUrl("https://example.com/contract.pdf")
                .status(ContractStatus.PENDING_SIGNATURE)
                .order(testOrder)
                .build();
    }

    @Test
    @DisplayName("Should process checkout successfully")
    void shouldProcessCheckoutSuccessfully() {
        // Given
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);
        when(cartService.calculateCartTotal("cart-123")).thenReturn(activeCart);
        when(invoiceService.generateInvoiceForOrder(testOrder)).thenReturn(testInvoice);
        when(invoiceService.updateInvoiceStatus("invoice-123", InvoiceStatus.SENT))
                .thenReturn(testInvoice);
        when(contractService.generateContractForOrder(testOrder)).thenReturn(testContract);
        when(articleService.getArticleById("article-123")).thenReturn(testArticle);

        // When
        Invoice result = checkoutService.processCheckout("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getInvoiceId()).isEqualTo("invoice-123");

        // Vérifier que le statut du panier a été mis à jour
        assertThat(activeCart.getStatus()).isEqualTo(CartStatus.CHECKED_OUT);

        // Vérifier que le statut des commandes a été mis à jour
        assertThat(testOrder.getStatus()).isEqualTo(OrderStatus.PENDING);
        assertThat(testOrder.getConfirmationDate()).isNotNull();

        // Vérifier les appels aux services
        verify(cartService).getCartWithOrders("account-123");
        verify(invoiceService).generateInvoiceForOrder(testOrder);
        verify(invoiceService).updateInvoiceStatus("invoice-123", InvoiceStatus.SENT);
        verify(contractService).generateContractForOrder(testOrder);
    }

    @Test
    @DisplayName("Should throw exception when account ID is null")
    void shouldThrowExceptionWhenAccountIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Account ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when cart is empty")
    void shouldThrowExceptionWhenCartIsEmpty() {
        // Given
        Cart emptyCart = Cart.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.ZERO)
                .orders(new ArrayList<>())
                .build();

        when(cartService.getCartWithOrders("account-123")).thenReturn(emptyCart);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart is empty");
    }

    @Test
    @DisplayName("Should throw exception when cart is not active")
    void shouldThrowExceptionWhenCartIsNotActive() {
        // Given
        activeCart.setStatus(CartStatus.CHECKED_OUT);
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart is not active");
    }

    @Test
    @DisplayName("Should throw exception when article is not available")
    void shouldThrowExceptionWhenArticleIsNotAvailable() {
        // Given
        testArticle.setAvailable(false);
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);
        when(articleService.getArticleById("article-123")).thenReturn(testArticle);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Article is not available: Test Car");

        verify(articleService).getArticleById("article-123");
    }

    @Test
    @DisplayName("Should throw exception when order has invalid quantity")
    void shouldThrowExceptionWhenOrderHasInvalidQuantity() {
        // Given
        testOrder.setQuantity(0);
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order quantity must be greater than 0");
    }

    @Test
    @DisplayName("Should throw exception when order has invalid unit price")
    void shouldThrowExceptionWhenOrderHasInvalidUnitPrice() {
        // Given
        testOrder.setUnitPrice(BigDecimal.ZERO);
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order unit price must be greater than 0");
    }

    @Test
    @DisplayName("Should throw exception when cart total is invalid")
    void shouldThrowExceptionWhenCartTotalIsInvalid() {
        // Given
        activeCart.setTotalAmount(BigDecimal.ZERO);
        when(cartService.getCartWithOrders("account-123")).thenReturn(activeCart);

        // When & Then
        assertThatThrownBy(() -> checkoutService.processCheckout("account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart total amount is invalid");
    }

    @Test
    @DisplayName("Should validate cart for checkout successfully")
    void shouldValidateCartForCheckoutSuccessfully() {
        // Given
        when(articleService.getArticleById("article-123")).thenReturn(testArticle);

        // When & Then - Should not throw exception
        checkoutService.validateCartForCheckout(activeCart);

        verify(articleService).getArticleById("article-123");
    }
}
