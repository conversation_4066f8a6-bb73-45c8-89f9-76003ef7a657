package sarl.eazycar.items.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.repository.InvoiceRepository;
import sarl.eazycar.items.domain.service.IInvoiceService;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * Implémentation du service Invoice
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class InvoiceService implements IInvoiceService {

    private final InvoiceRepository invoiceRepository;

    @Override
    public Invoice generateInvoiceForOrder(Order order) throws FunctionalErrorException {
        if (Objects.isNull(order)) {
            throw new FunctionalErrorException("Order cannot be null");
        }
        if (Strings.isBlank(order.getOrderId())) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        // Vérifier qu'une facture n'existe pas déjà pour cette commande
        invoiceRepository.findByOrderId(order.getOrderId())
                .ifPresent(existingInvoice -> {
                    throw new FunctionalErrorException(
                        "Invoice already exists for order: " + order.getOrderId());
                });

        Invoice invoice = Invoice.builder()
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30)) // 30 jours pour payer
                .status(InvoiceStatus.DRAFT)
                .order(order)
                .build();

        return invoiceRepository.save(invoice);
    }

    @Override
    @Transactional(readOnly = true)
    public Invoice getInvoiceByOrderId(String orderId) throws FunctionalErrorException {
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        return invoiceRepository.findByOrderId(orderId)
                .orElseThrow(() -> new FunctionalErrorException("Invoice not found for order: " + orderId));
    }

    @Override
    @Transactional(readOnly = true)
    public Invoice getInvoiceById(String invoiceId) throws FunctionalErrorException {
        if (Strings.isBlank(invoiceId)) {
            throw new FunctionalErrorException("Invoice ID cannot be null or empty");
        }

        return invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new FunctionalErrorException("Invoice not found: " + invoiceId));
    }

    @Override
    public Invoice updateInvoiceStatus(String invoiceId, InvoiceStatus newStatus) throws FunctionalErrorException {
        if (Strings.isBlank(invoiceId)) {
            throw new FunctionalErrorException("Invoice ID cannot be null or empty");
        }
        if (Objects.isNull(newStatus)) {
            throw new FunctionalErrorException("New status cannot be null");
        }

        Invoice invoice = getInvoiceById(invoiceId);
        
        // Valider la transition de statut
        validateStatusTransition(invoice.getStatus(), newStatus);
        
        invoice.setStatus(newStatus);

        return invoiceRepository.save(invoice);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Invoice> getInvoicesByStatus(InvoiceStatus status) {
        if (Objects.isNull(status)) {
            throw new FunctionalErrorException("Status cannot be null");
        }

        return invoiceRepository.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Invoice> getOverdueInvoices() {
        return invoiceRepository.findOverdueInvoices(LocalDate.now());
    }

    private void validateStatusTransition(InvoiceStatus currentStatus, InvoiceStatus newStatus) {
        // Règles de transition de statut pour les factures
        switch (currentStatus) {
            case DRAFT:
                if (newStatus != InvoiceStatus.SENT) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from DRAFT to " + newStatus);
                }
                break;
            case SENT:
                if (newStatus != InvoiceStatus.PAID && newStatus != InvoiceStatus.OVERDUE) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from SENT to " + newStatus);
                }
                break;
            case OVERDUE:
                if (newStatus != InvoiceStatus.PAID) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from OVERDUE to " + newStatus);
                }
                break;
            case PAID:
                throw new FunctionalErrorException(
                    "Cannot change status of a paid invoice");
            default:
                throw new FunctionalErrorException("Unknown invoice status: " + currentStatus);
        }
    }
}
