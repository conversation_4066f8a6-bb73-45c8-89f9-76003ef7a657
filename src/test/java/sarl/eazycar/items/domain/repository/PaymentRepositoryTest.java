package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Test class for PaymentRepository */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class PaymentRepositoryTest {

  @Autowired private PaymentRepository paymentRepository;

  @Autowired private ArticleRepository articleRepository;

  @Autowired private OrderRepository orderRepository;

  @Autowired private InvoiceRepository invoiceRepository;

  private Article createArticle(String articleId) {
    Article article = new Article();
    article.setArticleId(articleId);
    article.setName("Test Article");
    article.setDescription("Test Description");
    return articleRepository.save(article);
  }

  private Order createOrder(String orderId) {
    Article article = createArticle("article-" + orderId);

    Purchase order = new Purchase();
    order.setOrderId(orderId);
    order.setOrderDate(LocalDateTime.now());
    order.setStatus(OrderStatus.CONFIRMED);
    order.setCurrency("EUR");
    order.setQuantity(1);
    order.setUnitPrice(BigDecimal.valueOf(100.00));
    order.setArticle(article);
    order.setAccountId("account-123");
    order.setShippingAddress("Test Address");
    order.setTotalAmount(BigDecimal.valueOf(100.00));
    return orderRepository.save(order);
  }

  private Invoice createInvoice(String invoiceId, String orderId) {
    Order order = createOrder(orderId);

    Invoice invoice =
        Invoice.builder()
            .invoiceId(invoiceId)
            .issueDate(LocalDate.now())
            .dueDate(LocalDate.now().plusDays(30))
            .status(InvoiceStatus.DRAFT)
            .order(order)
            .build();
    return invoiceRepository.save(invoice);
  }

  @Test
  void save_payment_successfully() {
    // Given
    Invoice invoice = createInvoice(null, "order-123");
    Payment payment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(150.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-123456")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice)
            .build();

    // When
    Payment savedPayment = paymentRepository.save(payment);

    // Then
    assertThat(savedPayment).isNotNull();
    assertThat(savedPayment.getPaymentId()).isNotNull();
    assertThat(savedPayment.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(150.00));
    assertThat(savedPayment.getPaymentMethod()).isEqualTo(PaymentMethod.CREDIT_CARD);
    assertThat(savedPayment.getTransactionId()).isEqualTo("txn-123456");
    assertThat(savedPayment.getStatus()).isEqualTo(PaymentStatus.SUCCESSFUL);
    assertThat(savedPayment.getCreatedDate()).isNotNull(); // From AuditEntity
  }

  @Test
  void find_payments_by_invoice_id() {
    // Given
    Invoice invoice = createInvoice(null, "order-456");

    Payment payment1 =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-001")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice)
            .build();

    Payment payment2 =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(50.00))
            .paymentMethod(PaymentMethod.BANK_TRANSFER)
            .transactionId("txn-002")
            .status(PaymentStatus.PENDING)
            .invoice(invoice)
            .build();

    paymentRepository.save(payment1);
    paymentRepository.save(payment2);

    // When
    List<Payment> payments = paymentRepository.findPaymentsByInvoiceId(invoice.getInvoiceId());

    // Then
    assertThat(payments).hasSize(2);
    assertThat(payments)
        .extracting(payment -> payment.getInvoice().getInvoiceId())
        .containsOnly(invoice.getInvoiceId());
  }

  @Test
  void find_payments_by_status() {
    // Given
    Invoice invoice1 = createInvoice("invoice-1", "order-1");
    Payment completedPayment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-001")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice1)
            .build();

    Invoice invoice2 = createInvoice("invoice-2", "order-2");
    Payment pendingPayment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(75.00))
            .paymentMethod(PaymentMethod.BANK_TRANSFER)
            .transactionId("txn-002")
            .status(PaymentStatus.PENDING)
            .invoice(invoice2)
            .build();

    paymentRepository.save(completedPayment);
    paymentRepository.save(pendingPayment);

    // When
    List<Payment> completedPayments = paymentRepository.findByStatus(PaymentStatus.SUCCESSFUL);
    List<Payment> pendingPayments = paymentRepository.findByStatus(PaymentStatus.PENDING);

    // Then
    assertThat(completedPayments).hasSize(1);
    assertThat(completedPayments.get(0).getStatus()).isEqualTo(PaymentStatus.SUCCESSFUL);

    assertThat(pendingPayments).hasSize(1);
    assertThat(pendingPayments.get(0).getStatus()).isEqualTo(PaymentStatus.PENDING);
  }

  @Test
  void find_payments_by_payment_method() {
    // Given
    Invoice invoice1 = createInvoice("invoice-1", "order-1");
    Payment creditCardPayment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-001")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice1)
            .build();

    Invoice invoice2 = createInvoice("invoice-2", "order-2");
    Payment bankTransferPayment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(75.00))
            .paymentMethod(PaymentMethod.BANK_TRANSFER)
            .transactionId("txn-002")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice2)
            .build();

    paymentRepository.save(creditCardPayment);
    paymentRepository.save(bankTransferPayment);

    // When
    List<Payment> creditCardPayments =
        paymentRepository.findByPaymentMethod(PaymentMethod.CREDIT_CARD);

    // Then
    assertThat(creditCardPayments).hasSize(1);
    assertThat(creditCardPayments)
        .extracting(Payment::getPaymentMethod)
        .containsOnly(PaymentMethod.CREDIT_CARD);
  }

  @Test
  void find_payments_by_payment_date_between() {
    // Given
    LocalDateTime startDate = LocalDateTime.now().minusDays(10);
    LocalDateTime endDate = LocalDateTime.now().minusDays(5);

    Invoice invoice1 = createInvoice("invoice-1", "order-1");
    Payment payment1 =
        Payment.builder()
            .paymentDate(LocalDateTime.now().minusDays(7)) // Within range
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-001")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice1)
            .build();

    Invoice invoice2 = createInvoice("invoice-2", "order-2");
    Payment payment2 =
        Payment.builder()
            .paymentDate(LocalDateTime.now().minusDays(8)) // Within range
            .amount(BigDecimal.valueOf(75.00))
            .paymentMethod(PaymentMethod.BANK_TRANSFER)
            .transactionId("txn-002")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice2)
            .build();

    paymentRepository.save(payment1);
    paymentRepository.save(payment2);

    // When
    List<Payment> paymentsInRange = paymentRepository.findByPaymentDateBetween(startDate, endDate);

    // Then
    assertThat(paymentsInRange).hasSize(2);
    assertThat(paymentsInRange)
        .extracting(Payment::getPaymentDate)
        .allMatch(date -> !date.isBefore(startDate) && !date.isAfter(endDate));
  }

  @Test
  void find_payments_by_transaction_id() {
    // Given
    String transactionId = "txn-unique-123";
    Invoice invoice = createInvoice("invoice-123", "order-123");
    Payment payment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId(transactionId)
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice)
            .build();

    paymentRepository.save(payment);

    // When
    Optional<Payment> foundPayment = paymentRepository.findByTransactionId(transactionId);

    // Then
    assertThat(foundPayment).isPresent();
    assertThat(foundPayment.get().getTransactionId()).isEqualTo(transactionId);
  }
}
