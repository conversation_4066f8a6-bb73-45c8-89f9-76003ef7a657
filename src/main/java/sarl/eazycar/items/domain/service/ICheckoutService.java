package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Invoice;

/**
 * Interface pour le service Checkout
 */
public interface ICheckoutService {
    
    /**
     * Processus de checkout complet
     * - Lit le Cart et ses Order pour un utilisateur
     * - Valide le contenu (disponibilité des articles, prix, etc.)
     * - Génère l'entité Invoice associée
     * - Met à jour le statut du Cart à CHECKED_OUT
     */
    Invoice processCheckout(String accountId) throws FunctionalErrorException;
    
    /**
     * Valider le contenu du panier avant checkout
     */
    void validateCartForCheckout(Cart cart) throws FunctionalErrorException;
}
