package sarl.eazycar.items.application.rest.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sarl.eazycar.items.application.dto.ContractDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.dto.OrderDto;
import sarl.eazycar.items.application.dto.PaymentDto;
import sarl.eazycar.items.application.rest.request.PaymentRequest;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API REST pour la gestion des commandes
 */
@Tag(name = "Order", description = "API de gestion des commandes")
@RequestMapping("/api/v1/orders")
public interface OrderApi {

    @Operation(summary = "Obtenir la liste des commandes d'un utilisateur")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste des commandes récupérée avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/account/{accountId}")
    ResponseEntity<List<OrderDto>> getOrdersByAccountId(
        @Parameter(description = "ID du compte utilisateur", required = true)
        @PathVariable String accountId
    );

    @Operation(summary = "Obtenir les détails d'une commande spécifique")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Détails de la commande récupérés avec succès"),
        @ApiResponse(responseCode = "404", description = "Commande non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{orderId}")
    ResponseEntity<OrderDto> getOrderById(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Mettre à jour le statut d'une commande")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statut de la commande mis à jour avec succès"),
        @ApiResponse(responseCode = "400", description = "Transition de statut invalide"),
        @ApiResponse(responseCode = "404", description = "Commande non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @PutMapping("/{orderId}/status")
    ResponseEntity<OrderDto> updateOrderStatus(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId,
        @Parameter(description = "Nouveau statut", required = true)
        @RequestParam OrderStatus status
    );

    @Operation(summary = "Obtenir la facture associée à une commande")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Facture récupérée avec succès"),
        @ApiResponse(responseCode = "404", description = "Facture non trouvée pour cette commande"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{orderId}/invoice")
    ResponseEntity<InvoiceDto> getInvoiceByOrderId(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Obtenir la liste des paiements pour une facture")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste des paiements récupérée avec succès"),
        @ApiResponse(responseCode = "404", description = "Facture non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{orderId}/invoice/payments")
    ResponseEntity<List<PaymentDto>> getPaymentsByOrderId(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Enregistrer un paiement pour une facture")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Paiement enregistré avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres de paiement invalides"),
        @ApiResponse(responseCode = "404", description = "Facture non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @PostMapping("/{orderId}/invoice/payments")
    ResponseEntity<PaymentDto> recordPayment(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId,
        @RequestBody PaymentRequest paymentRequest
    );

    @Operation(summary = "Obtenir le contrat associé à une commande")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contrat récupéré avec succès"),
        @ApiResponse(responseCode = "404", description = "Contrat non trouvé pour cette commande"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @GetMapping("/{orderId}/contract")
    ResponseEntity<ContractDto> getContractByOrderId(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Enregistrer la signature d'un contrat")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Signature du contrat enregistrée avec succès"),
        @ApiResponse(responseCode = "400", description = "Contrat non signable"),
        @ApiResponse(responseCode = "404", description = "Contrat non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    @PutMapping("/{orderId}/contract/sign")
    ResponseEntity<ContractDto> signContract(
        @Parameter(description = "ID de la commande", required = true)
        @PathVariable String orderId,
        @Parameter(description = "Date et heure de signature", required = false)
        @RequestParam(required = false) LocalDateTime signatureDate
    );
}
