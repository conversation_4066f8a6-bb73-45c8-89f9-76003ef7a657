package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO pour Order
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDto {
    
    private String orderId;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime orderDate;
    
    private OrderStatus status;
    
    private String currency;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime confirmationDate;
    
    private Integer quantity;
    
    private BigDecimal unitPrice;
    
    private String articleId;
    
    private String cartId;
    
    private String priceId;
    
    private String accountId;
    
    private String orderType; // PURCHASE ou RENTAL
    
    // Champs spécifiques à Purchase
    private String shippingAddress;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private java.time.LocalDate estimatedDeliveryDate;
    
    // Champs spécifiques à Rental
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endDate;
    
    private String pickupLocation;
    
    private String dropoffLocation;
    
    private BigDecimal totalAmount;
    
    // Relations
    private ArticleDto article;
}
