#!/bin/bash

echo "🚀 Exécution des tests pour l'application items-app"
echo "=================================================="

# Couleurs pour l'affichage
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}📋 Tests unitaires des services...${NC}"
echo ""

# Tests unitaires des services
echo "🧪 CartServiceTest"
mvn test -Dtest=CartServiceTest -q -s settings.xml

echo "🧪 CheckoutServiceTest"
mvn test -Dtest=CheckoutServiceTest -q -s settings.xml

echo "🧪 InvoiceServiceTest"
mvn test -Dtest=InvoiceServiceTest -q -s settings.xml

echo "🧪 PaymentServiceTest"
mvn test -Dtest=PaymentServiceTest -q -s settings.xml

echo ""
echo -e "${YELLOW}🌐 Tests d'intégration des contrôleurs REST...${NC}"
echo ""

# Tests d'intégration des contrôleurs
echo "🧪 CartResourceTest"
mvn test -Dtest=CartResourceTest -q -s settings.xml

echo "🧪 OrderResourceTest"
mvn test -Dtest=OrderResourceTest -q -s settings.xml

echo ""
echo -e "${YELLOW}📊 Exécution de tous les tests avec rapport de couverture...${NC}"
echo ""

# Exécuter tous les tests avec rapport
mvn clean test -s settings.xml

echo ""
echo -e "${GREEN}✅ Tests terminés !${NC}"
echo ""
echo "📈 Pour voir le rapport de couverture détaillé :"
echo "   - Ouvrir target/site/jacoco/index.html dans un navigateur"
echo ""
echo "🔍 Pour exécuter des tests spécifiques :"
echo "   ./mvnw test -Dtest=NomDuTest"
echo ""
echo "🏃‍♂️ Pour exécuter les tests en mode watch :"
echo "   ./mvnw test -Dspring.devtools.restart.enabled=true"
