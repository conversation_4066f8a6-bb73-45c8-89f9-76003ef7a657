package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.ContractRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour ContractService
 */
@ExtendWith(MockitoExtension.class)
class ContractServiceTest {

    @Mock
    private ContractRepository contractRepository;

    @InjectMocks
    private ContractService contractService;

    private Purchase testOrder;
    private Contract testContract;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .description("Test Description")
                .available(true)
                .build();

        testOrder = Purchase.builder()
                .orderId("order-123")
                .accountId("account-123")
                .status(OrderStatus.PENDING)
                .quantity(1)
                .unitPrice(BigDecimal.valueOf(100.00))
                .currency("EUR")
                .article(testArticle)
                .shippingAddress("123 Test Street")
                .build();

        testContract = Contract.builder()
                .contractId("contract-123")
                .orderId("order-123")
                .status(ContractStatus.DRAFT)
                .contractContent("Test contract content")
                .creationDate(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("Should generate contract for order successfully")
    void shouldGenerateContractForOrderSuccessfully() {
        // Given
        when(contractRepository.save(any(Contract.class))).thenReturn(testContract);

        // When
        Contract result = contractService.generateContractForOrder(testOrder);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrderId()).isEqualTo("order-123");
        assertThat(result.getStatus()).isEqualTo(ContractStatus.DRAFT);
        assertThat(result.getContractContent()).contains("Test Car");
        verify(contractRepository).save(any(Contract.class));
    }

    @Test
    @DisplayName("Should throw exception when order is null")
    void shouldThrowExceptionWhenOrderIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.generateContractForOrder(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order cannot be null");
    }

    @Test
    @DisplayName("Should throw exception when order has no article")
    void shouldThrowExceptionWhenOrderHasNoArticle() {
        // Given
        testOrder.setArticle(null);

        // When & Then
        assertThatThrownBy(() -> contractService.generateContractForOrder(testOrder))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order must have an associated article");
    }

    @Test
    @DisplayName("Should get contract by order ID successfully")
    void shouldGetContractByOrderIdSuccessfully() {
        // Given
        when(contractRepository.findByOrderId("order-123")).thenReturn(Optional.of(testContract));

        // When
        Contract result = contractService.getContractByOrderId("order-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrderId()).isEqualTo("order-123");
        verify(contractRepository).findByOrderId("order-123");
    }

    @Test
    @DisplayName("Should throw exception when contract not found by order ID")
    void shouldThrowExceptionWhenContractNotFoundByOrderId() {
        // Given
        when(contractRepository.findByOrderId("order-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> contractService.getContractByOrderId("order-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract not found for order: order-123");
    }

    @Test
    @DisplayName("Should throw exception when order ID is null")
    void shouldThrowExceptionWhenOrderIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractByOrderId(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when order ID is empty")
    void shouldThrowExceptionWhenOrderIdIsEmpty() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractByOrderId(""))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should get contract by ID successfully")
    void shouldGetContractByIdSuccessfully() {
        // Given
        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));

        // When
        Contract result = contractService.getContractById("contract-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContractId()).isEqualTo("contract-123");
        verify(contractRepository).findById("contract-123");
    }

    @Test
    @DisplayName("Should throw exception when contract not found by ID")
    void shouldThrowExceptionWhenContractNotFoundById() {
        // Given
        when(contractRepository.findById("contract-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> contractService.getContractById("contract-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract not found: contract-123");
    }

    @Test
    @DisplayName("Should throw exception when contract ID is null")
    void shouldThrowExceptionWhenContractIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractById(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should sign contract successfully")
    void shouldSignContractSuccessfully() {
        // Given
        LocalDateTime signatureDate = LocalDateTime.now();
        Contract signedContract = Contract.builder()
                .contractId("contract-123")
                .orderId("order-123")
                .status(ContractStatus.SIGNED)
                .contractContent("Test contract content")
                .creationDate(LocalDateTime.now())
                .signatureDate(signatureDate)
                .build();

        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));
        when(contractRepository.save(any(Contract.class))).thenReturn(signedContract);

        // When
        Contract result = contractService.signContract("contract-123", signatureDate);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(ContractStatus.SIGNED);
        assertThat(result.getSignatureDate()).isEqualTo(signatureDate);
        verify(contractRepository).findById("contract-123");
        verify(contractRepository).save(any(Contract.class));
    }

    @Test
    @DisplayName("Should throw exception when trying to sign already signed contract")
    void shouldThrowExceptionWhenTryingToSignAlreadySignedContract() {
        // Given
        testContract.setStatus(ContractStatus.SIGNED);
        testContract.setSignatureDate(LocalDateTime.now());
        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));

        // When & Then
        assertThatThrownBy(() -> contractService.signContract("contract-123", LocalDateTime.now()))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract is already signed");
    }

    @Test
    @DisplayName("Should throw exception when signature date is null")
    void shouldThrowExceptionWhenSignatureDateIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.signContract("contract-123", null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Signature date cannot be null");
    }

    @Test
    @DisplayName("Should get contracts by status successfully")
    void shouldGetContractsByStatusSuccessfully() {
        // Given
        List<Contract> contracts = List.of(testContract);
        when(contractRepository.findByStatus(ContractStatus.DRAFT)).thenReturn(contracts);

        // When
        List<Contract> result = contractService.getContractsByStatus(ContractStatus.DRAFT);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getStatus()).isEqualTo(ContractStatus.DRAFT);
        verify(contractRepository).findByStatus(ContractStatus.DRAFT);
    }

    @Test
    @DisplayName("Should return empty list when no contracts found by status")
    void shouldReturnEmptyListWhenNoContractsFoundByStatus() {
        // Given
        when(contractRepository.findByStatus(ContractStatus.SIGNED)).thenReturn(List.of());

        // When
        List<Contract> result = contractService.getContractsByStatus(ContractStatus.SIGNED);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        verify(contractRepository).findByStatus(ContractStatus.SIGNED);
    }

    @Test
    @DisplayName("Should throw exception when status is null")
    void shouldThrowExceptionWhenStatusIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractsByStatus(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }

    @Test
    @DisplayName("Should generate contract content correctly for Purchase")
    void shouldGenerateContractContentCorrectlyForPurchase() {
        // Given
        when(contractRepository.save(any(Contract.class))).thenAnswer(invocation -> {
            Contract contract = invocation.getArgument(0);
            return contract;
        });

        // When
        Contract result = contractService.generateContractForOrder(testOrder);

        // Then
        verify(contractRepository).save(argThat(contract -> {
            assertThat(contract.getContractContent()).contains("PURCHASE CONTRACT");
            assertThat(contract.getContractContent()).contains("Test Car");
            assertThat(contract.getContractContent()).contains("100.00 EUR");
            assertThat(contract.getContractContent()).contains("123 Test Street");
            return true;
        }));
    }

    @Test
    @DisplayName("Should validate contract status transition")
    void shouldValidateContractStatusTransition() {
        // Given - Contract already signed
        testContract.setStatus(ContractStatus.SIGNED);
        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));

        // When & Then
        assertThatThrownBy(() -> contractService.signContract("contract-123", LocalDateTime.now()))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract is already signed");
    }
}
