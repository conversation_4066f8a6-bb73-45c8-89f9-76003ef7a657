package sarl.eazycar.items.application.rest.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sarl.eazycar.items.application.dto.CartDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.rest.request.OrderRequest;

/** API REST pour la gestion des paniers */
@Tag(name = "Cart", description = "API de gestion des paniers")
@RequestMapping("/api/v1/carts")
public interface CartApi {

  @Operation(summary = "Obtenir ou créer un panier actif pour un utilisateur")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Panier récupéré ou créé avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @GetMapping("/{accountId}")
  ResponseEntity<CartDto> getOrCreateCart(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId);

  @Operation(summary = "Ajouter une commande au panier")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Commande ajoutée au panier avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "404", description = "Article non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @PostMapping("/{accountId}/orders")
  ResponseEntity<CartDto> addOrderToCart(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId,
      @RequestBody OrderRequest orderRequest);

  @Operation(summary = "Mettre à jour la quantité d'une commande dans le panier")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Quantité mise à jour avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "404", description = "Commande non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @PutMapping("/{accountId}/orders/{orderId}/quantity")
  ResponseEntity<CartDto> updateOrderQuantity(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId,
      @Parameter(description = "ID de la commande", required = true) @PathVariable String orderId,
      @Parameter(description = "Nouvelle quantité", required = true) @RequestParam
          Integer quantity);

  @Operation(summary = "Retirer une commande du panier")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Commande retirée avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "404", description = "Commande non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @DeleteMapping("/{accountId}/orders/{orderId}")
  ResponseEntity<CartDto> removeOrderFromCart(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId,
      @Parameter(description = "ID de la commande", required = true) @PathVariable String orderId);

  @Operation(summary = "Vider le panier")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Panier vidé avec succès"),
        @ApiResponse(responseCode = "400", description = "Paramètres invalides"),
        @ApiResponse(responseCode = "404", description = "Panier non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @DeleteMapping("/{accountId}/clear")
  ResponseEntity<CartDto> clearCart(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId);

  @Operation(summary = "Processus de checkout - Finaliser le panier")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Checkout effectué avec succès"),
        @ApiResponse(responseCode = "400", description = "Panier invalide pour le checkout"),
        @ApiResponse(responseCode = "404", description = "Panier non trouvé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
      })
  @PostMapping("/{accountId}/checkout")
  ResponseEntity<List<InvoiceDto>> checkout(
      @Parameter(description = "ID du compte utilisateur", required = true) @PathVariable
          String accountId);
}
