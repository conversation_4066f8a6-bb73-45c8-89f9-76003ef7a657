package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.OrderRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour OrderService
 */
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock
    private OrderRepository orderRepository;

    @InjectMocks
    private OrderService orderService;

    private Purchase testPurchase;
    private Rental testRental;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .description("Test Description")
                .available(true)
                .build();

        testPurchase = Purchase.builder()
                .orderId("order-123")
                .accountId("account-123")
                .status(OrderStatus.PENDING)
                .quantity(1)
                .unitPrice(BigDecimal.valueOf(100.00))
                .currency("EUR")
                .article(testArticle)
                .shippingAddress("123 Test Street")
                .build();

        testRental = Rental.builder()
                .orderId("rental-123")
                .accountId("account-123")
                .status(OrderStatus.PENDING)
                .quantity(1)
                .unitPrice(BigDecimal.valueOf(50.00))
                .currency("EUR")
                .article(testArticle)
                .startDate(LocalDate.now().plusDays(1))
                .endDate(LocalDate.now().plusDays(7))
                .duration(6)
                .build();
    }

    @Test
    @DisplayName("Should create order successfully")
    void shouldCreateOrderSuccessfully() {
        // Given
        when(orderRepository.save(any(Purchase.class))).thenReturn(testPurchase);

        // When
        Purchase result = (Purchase) orderService.createOrder(testPurchase);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrderId()).isEqualTo("order-123");
        assertThat(result.getStatus()).isEqualTo(OrderStatus.PENDING);
        verify(orderRepository).save(testPurchase);
    }

    @Test
    @DisplayName("Should throw exception when creating order with existing ID")
    void shouldThrowExceptionWhenCreatingOrderWithExistingId() {
        // Given
        testPurchase.setOrderId("existing-id");

        // When & Then
        assertThatThrownBy(() -> orderService.createOrder(testPurchase))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("A new order cannot already have an ID");
    }

    @Test
    @DisplayName("Should throw exception when order is null")
    void shouldThrowExceptionWhenOrderIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.createOrder(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order cannot be null");
    }

    @Test
    @DisplayName("Should get order by ID successfully")
    void shouldGetOrderByIdSuccessfully() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When
        Purchase result = (Purchase) orderService.getOrderById("order-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrderId()).isEqualTo("order-123");
        verify(orderRepository).findById("order-123");
    }

    @Test
    @DisplayName("Should throw exception when order not found")
    void shouldThrowExceptionWhenOrderNotFound() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> orderService.getOrderById("order-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order not found: order-123");
    }

    @Test
    @DisplayName("Should throw exception when order ID is null")
    void shouldThrowExceptionWhenOrderIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrderById(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when order ID is empty")
    void shouldThrowExceptionWhenOrderIdIsEmpty() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrderById(""))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should get orders by account ID successfully")
    void shouldGetOrdersByAccountIdSuccessfully() {
        // Given
        List<Purchase> orders = List.of(testPurchase);
        when(orderRepository.findByAccountIdOrderByCreationDateDesc("account-123")).thenReturn(orders);

        // When
        List<Purchase> result = (List<Purchase>) orderService.getOrdersByAccountId("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getAccountId()).isEqualTo("account-123");
        verify(orderRepository).findByAccountIdOrderByCreationDateDesc("account-123");
    }

    @Test
    @DisplayName("Should return empty list when no orders found for account")
    void shouldReturnEmptyListWhenNoOrdersFoundForAccount() {
        // Given
        when(orderRepository.findByAccountIdOrderByCreationDateDesc("account-123")).thenReturn(List.of());

        // When
        List result = orderService.getOrdersByAccountId("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        verify(orderRepository).findByAccountIdOrderByCreationDateDesc("account-123");
    }

    @Test
    @DisplayName("Should throw exception when account ID is null")
    void shouldThrowExceptionWhenAccountIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrdersByAccountId(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Account ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should update order status successfully")
    void shouldUpdateOrderStatusSuccessfully() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testPurchase);

        // When
        Purchase result = (Purchase) orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
        verify(orderRepository).findById("order-123");
        verify(orderRepository).save(testPurchase);
    }

    @Test
    @DisplayName("Should throw exception when updating to invalid status")
    void shouldThrowExceptionWhenUpdatingToInvalidStatus() {
        // Given
        testPurchase.setStatus(OrderStatus.CANCELLED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from CANCELLED to CONFIRMED");
    }

    @Test
    @DisplayName("Should throw exception when new status is null")
    void shouldThrowExceptionWhenNewStatusIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }

    @Test
    @DisplayName("Should get orders by status successfully")
    void shouldGetOrdersByStatusSuccessfully() {
        // Given
        List<Purchase> orders = List.of(testPurchase);
        when(orderRepository.findByStatus(OrderStatus.PENDING)).thenReturn(orders);

        // When
        List result = orderService.getOrdersByStatus(OrderStatus.PENDING);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        verify(orderRepository).findByStatus(OrderStatus.PENDING);
    }

    @Test
    @DisplayName("Should throw exception when status is null for getOrdersByStatus")
    void shouldThrowExceptionWhenStatusIsNullForGetOrdersByStatus() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrdersByStatus(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }

    @Test
    @DisplayName("Should validate status transition from PENDING to CONFIRMED")
    void shouldValidateStatusTransitionFromPendingToConfirmed() {
        // Given
        testPurchase.setStatus(OrderStatus.PENDING);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testPurchase);

        // When
        Purchase result = (Purchase) orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
    }

    @Test
    @DisplayName("Should validate status transition from PENDING to CANCELLED")
    void shouldValidateStatusTransitionFromPendingToCancelled() {
        // Given
        testPurchase.setStatus(OrderStatus.PENDING);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testPurchase);

        // When
        Purchase result = (Purchase) orderService.updateOrderStatus("order-123", OrderStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CANCELLED);
    }

    @Test
    @DisplayName("Should validate status transition from CONFIRMED to CANCELLED")
    void shouldValidateStatusTransitionFromConfirmedToCancelled() {
        // Given
        testPurchase.setStatus(OrderStatus.CONFIRMED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testPurchase);

        // When
        Purchase result = (Purchase) orderService.updateOrderStatus("order-123", OrderStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CANCELLED);
    }

    @Test
    @DisplayName("Should reject invalid status transition from CONFIRMED to PENDING")
    void shouldRejectInvalidStatusTransitionFromConfirmedToPending() {
        // Given
        testPurchase.setStatus(OrderStatus.CONFIRMED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.PENDING))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from CONFIRMED to PENDING");
    }

    @Test
    @DisplayName("Should handle rental order correctly")
    void shouldHandleRentalOrderCorrectly() {
        // Given
        when(orderRepository.save(any(Rental.class))).thenReturn(testRental);

        // When
        Rental result = (Rental) orderService.createOrder(testRental);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isInstanceOf(Rental.class);
        assertThat(result.getStartDate()).isEqualTo(testRental.getStartDate());
        assertThat(result.getEndDate()).isEqualTo(testRental.getEndDate());
        assertThat(result.getDuration()).isEqualTo(6);
        verify(orderRepository).save(testRental);
    }

    @Test
    @DisplayName("Should calculate total amount correctly for purchase")
    void shouldCalculateTotalAmountCorrectlyForPurchase() {
        // Given
        testPurchase.setQuantity(2);
        testPurchase.setUnitPrice(BigDecimal.valueOf(100.00));

        // When
        BigDecimal totalAmount = testPurchase.getTotalAmount();

        // Then
        assertThat(totalAmount).isEqualTo(BigDecimal.valueOf(200.00));
    }

    @Test
    @DisplayName("Should calculate total amount correctly for rental")
    void shouldCalculateTotalAmountCorrectlyForRental() {
        // Given
        testRental.setQuantity(1);
        testRental.setUnitPrice(BigDecimal.valueOf(50.00));
        testRental.setDuration(6);

        // When
        BigDecimal totalAmount = testRental.getTotalAmount();

        // Then
        assertThat(totalAmount).isEqualTo(BigDecimal.valueOf(300.00)); // 50 * 1 * 6
    }
}
