package sarl.eazycar.items.application.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.CartDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.rest.request.OrderRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.ICartService;
import sarl.eazycar.items.domain.service.ICheckoutService;
import sarl.eazycar.items.infrastructure.factory.OrderFactory;
import sarl.eazycar.items.infrastructure.mapper.CartMapper;
import sarl.eazycar.items.infrastructure.mapper.InvoiceMapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Tests d'intégration pour CartResource
 */
@WebMvcTest(CartResource.class)
class CartResourceTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ICartService cartService;

    @MockBean
    private ICheckoutService checkoutService;

    @MockBean
    private IArticleService articleService;

    @MockBean
    private OrderFactory orderFactory;

    @MockBean
    private CartMapper cartMapper;

    @MockBean
    private InvoiceMapper invoiceMapper;

    private Cart testCart;
    private CartDto testCartDto;
    private Purchase testOrder;
    private Article testArticle;
    private Invoice testInvoice;
    private InvoiceDto testInvoiceDto;

    @BeforeEach
    void setUp() {
        // Créer un article de test
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .available(true)
                .build();

        // Créer une commande de test
        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setArticle(testArticle);
        testOrder.setOrderDate(LocalDateTime.now());
        testOrder.setStatus(OrderStatus.IN_CART);

        // Créer un panier de test
        testCart = Cart.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.valueOf(100.00))
                .orders(new ArrayList<>(List.of(testOrder)))
                .build();

        // Créer un DTO de panier de test
        testCartDto = CartDto.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.valueOf(100.00))
                .orders(List.of())
                .build();

        // Créer une facture de test
        testInvoice = Invoice.builder()
                .invoiceId("invoice-123")
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30))
                .status(InvoiceStatus.SENT)
                .order(testOrder)
                .build();

        // Créer un DTO de facture de test
        testInvoiceDto = InvoiceDto.builder()
                .invoiceId("invoice-123")
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30))
                .status(InvoiceStatus.SENT)
                .orderId("order-123")
                .build();
    }

    @Test
    @DisplayName("Should get or create cart successfully")
    void shouldGetOrCreateCartSuccessfully() throws Exception {
        // Given
        when(cartService.getOrCreateActiveCart("account-123")).thenReturn(testCart);
        when(cartMapper.toDto(testCart)).thenReturn(testCartDto);

        // When & Then
        mockMvc.perform(get("/api/v1/carts/account-123"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.cartId").value("cart-123"))
                .andExpect(jsonPath("$.accountId").value("account-123"))
                .andExpect(jsonPath("$.status").value("ACTIVE"))
                .andExpect(jsonPath("$.totalAmount").value(100.00));
    }

    @Test
    @DisplayName("Should add order to cart successfully")
    void shouldAddOrderToCartSuccessfully() throws Exception {
        // Given
        OrderRequest orderRequest = OrderRequest.builder()
                .articleId("article-123")
                .quantity(1)
                .unitPrice(BigDecimal.valueOf(100.00))
                .currency("EUR")
                .orderType("PURCHASE")
                .build();

        when(articleService.getArticleById("article-123")).thenReturn(testArticle);
        when(orderFactory.createOrderFromRequest(any(OrderRequest.class), any(Article.class)))
                .thenReturn(testOrder);
        when(cartService.addOrderToCart("account-123", testOrder)).thenReturn(testCart);
        when(cartMapper.toDto(testCart)).thenReturn(testCartDto);

        // When & Then
        mockMvc.perform(post("/api/v1/carts/account-123/orders")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(orderRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.cartId").value("cart-123"));
    }

    @Test
    @DisplayName("Should update order quantity successfully")
    void shouldUpdateOrderQuantitySuccessfully() throws Exception {
        // Given
        when(cartService.updateOrderQuantity("account-123", "order-123", 3)).thenReturn(testCart);
        when(cartMapper.toDto(testCart)).thenReturn(testCartDto);

        // When & Then
        mockMvc.perform(put("/api/v1/carts/account-123/orders/order-123/quantity")
                        .param("quantity", "3"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.cartId").value("cart-123"));
    }

    @Test
    @DisplayName("Should remove order from cart successfully")
    void shouldRemoveOrderFromCartSuccessfully() throws Exception {
        // Given
        when(cartService.removeOrderFromCart("account-123", "order-123")).thenReturn(testCart);
        when(cartMapper.toDto(testCart)).thenReturn(testCartDto);

        // When & Then
        mockMvc.perform(delete("/api/v1/carts/account-123/orders/order-123"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.cartId").value("cart-123"));
    }

    @Test
    @DisplayName("Should clear cart successfully")
    void shouldClearCartSuccessfully() throws Exception {
        // Given
        Cart emptyCart = Cart.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.ZERO)
                .orders(new ArrayList<>())
                .build();

        CartDto emptyCartDto = CartDto.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.ZERO)
                .orders(List.of())
                .build();

        when(cartService.clearCart("account-123")).thenReturn(emptyCart);
        when(cartMapper.toDto(emptyCart)).thenReturn(emptyCartDto);

        // When & Then
        mockMvc.perform(delete("/api/v1/carts/account-123/clear"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.cartId").value("cart-123"))
                .andExpect(jsonPath("$.totalAmount").value(0.0));
    }

    @Test
    @DisplayName("Should process checkout successfully")
    void shouldProcessCheckoutSuccessfully() throws Exception {
        // Given
        when(checkoutService.processCheckout("account-123")).thenReturn(testInvoice);
        when(invoiceMapper.toDto(testInvoice)).thenReturn(testInvoiceDto);

        // When & Then
        mockMvc.perform(post("/api/v1/carts/account-123/checkout"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.invoiceId").value("invoice-123"))
                .andExpect(jsonPath("$.status").value("SENT"))
                .andExpect(jsonPath("$.orderId").value("order-123"));
    }

    @Test
    @DisplayName("Should return bad request for invalid order request")
    void shouldReturnBadRequestForInvalidOrderRequest() throws Exception {
        // Given
        OrderRequest invalidRequest = OrderRequest.builder()
                .articleId("") // Invalid: empty article ID
                .quantity(0) // Invalid: zero quantity
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/carts/account-123/orders")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }
}
