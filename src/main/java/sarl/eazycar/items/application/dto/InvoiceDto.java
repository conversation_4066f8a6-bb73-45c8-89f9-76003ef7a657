package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;

import java.time.LocalDate;
import java.util.List;

/**
 * DTO pour Invoice
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDto {
    
    private String invoiceId;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate issueDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dueDate;
    
    private InvoiceStatus status;
    
    private String orderId;
    
    private OrderDto order;
    
    private List<PaymentDto> payments;
}
