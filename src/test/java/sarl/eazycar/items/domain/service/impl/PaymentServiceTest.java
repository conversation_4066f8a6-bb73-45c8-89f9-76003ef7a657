package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;
import sarl.eazycar.items.domain.repository.InvoiceRepository;
import sarl.eazycar.items.domain.repository.PaymentRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour PaymentService
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PaymentServiceTest {

    @Mock
    private PaymentRepository paymentRepository;

    @Mock
    private InvoiceRepository invoiceRepository;

    @InjectMocks
    private PaymentService paymentService;

    private Invoice testInvoice;
    private Payment testPayment;
    private Purchase testOrder;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        // Créer un article de test
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .available(true)
                .build();

        // Créer une commande de test
        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setArticle(testArticle);
        testOrder.setOrderDate(LocalDateTime.now());
        testOrder.setStatus(OrderStatus.CONFIRMED);

        // Créer une facture de test
        testInvoice = Invoice.builder()
                .invoiceId("invoice-123")
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30))
                .status(InvoiceStatus.SENT)
                .order(testOrder)
                .payments(new ArrayList<>())
                .build();

        // Créer un paiement de test
        testPayment = Payment.builder()
                .paymentId("payment-123")
                .paymentDate(LocalDateTime.now())
                .amount(BigDecimal.valueOf(100.00))
                .paymentMethod(PaymentMethod.CREDIT_CARD)
                .transactionId("txn-123456")
                .status(PaymentStatus.SUCCESSFUL)
                .invoice(testInvoice)
                .build();
    }

    @Test
    @DisplayName("Should record payment successfully")
    void shouldRecordPaymentSuccessfully() {
        // Given
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));
        when(paymentRepository.findByTransactionId("txn-123456")).thenReturn(Optional.empty());
        when(paymentRepository.save(any(Payment.class))).thenReturn(testPayment);
        when(invoiceRepository.save(any(Invoice.class))).thenReturn(testInvoice);

        // When
        Payment result = paymentService.recordPayment(
                "invoice-123",
                BigDecimal.valueOf(100.00),
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        );

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentId()).isEqualTo("payment-123");
        assertThat(result.getAmount()).isEqualTo(BigDecimal.valueOf(100.00));
        assertThat(result.getPaymentMethod()).isEqualTo(PaymentMethod.CREDIT_CARD);
        assertThat(result.getTransactionId()).isEqualTo("txn-123456");
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.SUCCESSFUL);

        verify(paymentRepository).save(any(Payment.class));
    }

    @Test
    @DisplayName("Should throw exception when invoice not found")
    void shouldThrowExceptionWhenInvoiceNotFound() {
        // Given
        when(invoiceRepository.findById("invalid-invoice")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> paymentService.recordPayment(
                "invalid-invoice",
                BigDecimal.valueOf(100.00),
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        ))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invoice not found: invalid-invoice");
    }

    @Test
    @DisplayName("Should throw exception when invoice is already paid")
    void shouldThrowExceptionWhenInvoiceIsAlreadyPaid() {
        // Given
        testInvoice.setStatus(InvoiceStatus.PAID);
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));

        // When & Then
        assertThatThrownBy(() -> paymentService.recordPayment(
                "invoice-123",
                BigDecimal.valueOf(100.00),
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        ))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invoice is already paid");
    }

    @Test
    @DisplayName("Should throw exception when transaction ID already exists")
    void shouldThrowExceptionWhenTransactionIdAlreadyExists() {
        // Given
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));
        when(paymentRepository.findByTransactionId("txn-123456")).thenReturn(Optional.of(testPayment));

        // When & Then
        assertThatThrownBy(() -> paymentService.recordPayment(
                "invoice-123",
                BigDecimal.valueOf(100.00),
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        ))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Payment with transaction ID already exists: txn-123456");
    }

    @Test
    @DisplayName("Should throw exception when amount is invalid")
    void shouldThrowExceptionWhenAmountIsInvalid() {
        // When & Then
        assertThatThrownBy(() -> paymentService.recordPayment(
                "invoice-123",
                BigDecimal.ZERO,
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        ))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Amount must be greater than zero");
    }

    @Test
    @DisplayName("Should get payments by invoice ID successfully")
    void shouldGetPaymentsByInvoiceIdSuccessfully() {
        // Given
        List<Payment> payments = List.of(testPayment);
        when(paymentRepository.findPaymentsByInvoiceId("invoice-123")).thenReturn(payments);

        // When
        List<Payment> result = paymentService.getPaymentsByInvoiceId("invoice-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getPaymentId()).isEqualTo("payment-123");
    }

    @Test
    @DisplayName("Should get payment by ID successfully")
    void shouldGetPaymentByIdSuccessfully() {
        // Given
        when(paymentRepository.findById("payment-123")).thenReturn(Optional.of(testPayment));

        // When
        Payment result = paymentService.getPaymentById("payment-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getPaymentId()).isEqualTo("payment-123");
    }

    @Test
    @DisplayName("Should throw exception when payment not found by ID")
    void shouldThrowExceptionWhenPaymentNotFoundById() {
        // Given
        when(paymentRepository.findById("invalid-payment")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> paymentService.getPaymentById("invalid-payment"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Payment not found: invalid-payment");
    }

    @Test
    @DisplayName("Should update payment status successfully")
    void shouldUpdatePaymentStatusSuccessfully() {
        // Given
        testPayment.setStatus(PaymentStatus.PENDING);
        when(paymentRepository.findById("payment-123")).thenReturn(Optional.of(testPayment));
        when(paymentRepository.save(any(Payment.class))).thenReturn(testPayment);

        // When
        Payment result = paymentService.updatePaymentStatus("payment-123", PaymentStatus.SUCCESSFUL);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.SUCCESSFUL);
        verify(paymentRepository).save(testPayment);
    }

    @Test
    @DisplayName("Should throw exception for invalid payment status transition")
    void shouldThrowExceptionForInvalidPaymentStatusTransition() {
        // Given
        testPayment.setStatus(PaymentStatus.SUCCESSFUL);
        when(paymentRepository.findById("payment-123")).thenReturn(Optional.of(testPayment));

        // When & Then
        assertThatThrownBy(() -> paymentService.updatePaymentStatus("payment-123", PaymentStatus.FAILED))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cannot change status of a successful payment");
    }

    @Test
    @DisplayName("Should get payments by status successfully")
    void shouldGetPaymentsByStatusSuccessfully() {
        // Given
        List<Payment> payments = List.of(testPayment);
        when(paymentRepository.findByStatus(PaymentStatus.SUCCESSFUL)).thenReturn(payments);

        // When
        List<Payment> result = paymentService.getPaymentsByStatus(PaymentStatus.SUCCESSFUL);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getPaymentId()).isEqualTo("payment-123");
    }

    @Test
    @DisplayName("Should get payment by transaction ID successfully")
    void shouldGetPaymentByTransactionIdSuccessfully() {
        // Given
        when(paymentRepository.findByTransactionId("txn-123456")).thenReturn(Optional.of(testPayment));

        // When
        Payment result = paymentService.getPaymentByTransactionId("txn-123456");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTransactionId()).isEqualTo("txn-123456");
    }

    @Test
    @DisplayName("Should throw exception when payment not found by transaction ID")
    void shouldThrowExceptionWhenPaymentNotFoundByTransactionId() {
        // Given
        when(paymentRepository.findByTransactionId("invalid-txn")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> paymentService.getPaymentByTransactionId("invalid-txn"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Payment not found for transaction: invalid-txn");
    }

    @Test
    @DisplayName("Should update invoice status to PAID when fully paid")
    void shouldUpdateInvoiceStatusToPaidWhenFullyPaid() {
        // Given
        testPayment.setAmount(BigDecimal.valueOf(100.00)); // Montant total de la commande
        testInvoice.getPayments().add(testPayment);

        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));
        when(paymentRepository.findByTransactionId("txn-123456")).thenReturn(Optional.empty());
        when(paymentRepository.save(any(Payment.class))).thenReturn(testPayment);
        when(invoiceRepository.save(any(Invoice.class))).thenReturn(testInvoice);

        // When
        paymentService.recordPayment(
                "invoice-123",
                BigDecimal.valueOf(100.00),
                PaymentMethod.CREDIT_CARD,
                "txn-123456"
        );

        // Then
        verify(invoiceRepository).save(testInvoice);
        assertThat(testInvoice.getStatus()).isEqualTo(InvoiceStatus.PAID);
    }
}
