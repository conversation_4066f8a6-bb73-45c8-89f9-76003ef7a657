package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * Test class for RentalRepository
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class RentalRepositoryTest {

    @Autowired
    private RentalRepository rentalRepository;

    @Autowired
    private ArticleRepository articleRepository;

    private Article createArticle(String articleId) {
        Article article = new Article();
        article.setArticleId(articleId);
        article.setName("Test Article");
        article.setDescription("Test Description");
        return articleRepository.save(article);
    }

    private Cart createCart(String cartId, String accountId) {
        Cart cart = new Cart();
        cart.setCartId(cartId);
        cart.setAccountId(accountId);
        cart.setStatus(CartStatus.ACTIVE);
        cart.setTotalAmount(BigDecimal.ZERO);
        return cart;
    }

    private Rental createRental(LocalDateTime orderDate, OrderStatus status, String currency,
                               Integer quantity, BigDecimal unitPrice, String articleId,
                               String accountId, LocalDateTime startDate, LocalDateTime endDate,
                               String pickupLocation, String dropoffLocation, BigDecimal totalAmount) {
        Article article = createArticle(articleId);

        Rental rental = new Rental();
        rental.setOrderDate(orderDate);
        rental.setStatus(status);
        rental.setCurrency(currency);
        rental.setQuantity(quantity);
        rental.setUnitPrice(unitPrice);
        rental.setArticle(article);
        rental.setAccountId(accountId);
        rental.setStartDate(startDate);
        rental.setEndDate(endDate);
        rental.setPickupLocation(pickupLocation);
        rental.setDropoffLocation(dropoffLocation);
        rental.setTotalAmount(totalAmount);
        return rental;
    }

    @Test
    void save_rental_successfully() {
        // Given
        LocalDateTime startDate = LocalDateTime.now().plusDays(1);
        LocalDateTime endDate = LocalDateTime.now().plusDays(3);
        
        Rental rental = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(50.00),
                "article-123",
                "account-456",
                startDate,
                endDate,
                "Downtown Office",
                "Airport Terminal",
                BigDecimal.valueOf(100.00)
        );

        // When
        Rental savedRental = rentalRepository.save(rental);

        // Then
        assertThat(savedRental).isNotNull();
        assertThat(savedRental.getOrderId()).isNotNull();
        assertThat(savedRental.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
        assertThat(savedRental.getStartDate()).isEqualTo(startDate);
        assertThat(savedRental.getEndDate()).isEqualTo(endDate);
        assertThat(savedRental.getPickupLocation()).isEqualTo("Downtown Office");
        assertThat(savedRental.getDropoffLocation()).isEqualTo("Airport Terminal");
        assertThat(savedRental.getTotalAmount()).isEqualByComparingTo(BigDecimal.valueOf(100.00));
    }

    @Test
    void find_rentals_by_account_id() {
        // Given
        String accountId = "account-test-123";
        
        Rental rental1 = createRental(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                accountId,
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(2),
                "Location A",
                "Location B",
                BigDecimal.valueOf(40.00)
        );

        Rental rental2 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                accountId,
                LocalDateTime.now().plusDays(5),
                LocalDateTime.now().plusDays(7),
                "Location C",
                "Location D",
                BigDecimal.valueOf(120.00)
        );

        rentalRepository.save(rental1);
        rentalRepository.save(rental2);

        // When
        List<Rental> rentals = rentalRepository.findByAccountId(accountId);

        // Then
        assertThat(rentals).hasSize(2);
        assertThat(rentals).extracting(Rental::getAccountId)
                .containsOnly(accountId);
    }

    @Test
    void find_rentals_by_status() {
        // Given
        Rental pendingRental = createRental(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                "account-1",
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(2),
                "Location A",
                "Location B",
                BigDecimal.valueOf(40.00)
        );

        Rental confirmedRental = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                "account-2",
                LocalDateTime.now().plusDays(3),
                LocalDateTime.now().plusDays(5),
                "Location C",
                "Location D",
                BigDecimal.valueOf(120.00)
        );

        rentalRepository.save(pendingRental);
        rentalRepository.save(confirmedRental);

        // When
        List<Rental> pendingRentals = rentalRepository.findByStatus(OrderStatus.PENDING);
        List<Rental> confirmedRentals = rentalRepository.findByStatus(OrderStatus.CONFIRMED);

        // Then
        assertThat(pendingRentals).hasSize(1);
        assertThat(pendingRentals.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
        
        assertThat(confirmedRentals).hasSize(1);
        assertThat(confirmedRentals.get(0).getStatus()).isEqualTo(OrderStatus.CONFIRMED);
    }

    @Test
    void find_rentals_by_start_date_between() {
        // Given
        LocalDateTime searchStartDate = LocalDateTime.now().plusDays(2);
        LocalDateTime searchEndDate = LocalDateTime.now().plusDays(8);
        
        Rental rental1 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                "account-1",
                LocalDateTime.now().plusDays(3), // Within range
                LocalDateTime.now().plusDays(5),
                "Location A",
                "Location B",
                BigDecimal.valueOf(80.00)
        );

        Rental rental2 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                "account-2",
                LocalDateTime.now().plusDays(10), // Outside range
                LocalDateTime.now().plusDays(12),
                "Location C",
                "Location D",
                BigDecimal.valueOf(120.00)
        );

        Rental rental3 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(50.00),
                "article-3",
                "account-3",
                LocalDateTime.now().plusDays(6), // Within range
                LocalDateTime.now().plusDays(8),
                "Location E",
                "Location F",
                BigDecimal.valueOf(100.00)
        );

        rentalRepository.save(rental1);
        rentalRepository.save(rental2);
        rentalRepository.save(rental3);

        // When
        List<Rental> rentalsInRange = rentalRepository.findByStartDateBetween(searchStartDate, searchEndDate);

        // Then
        assertThat(rentalsInRange).hasSize(2);
        assertThat(rentalsInRange).extracting(Rental::getStartDate)
                .allMatch(date -> !date.isBefore(searchStartDate) && !date.isAfter(searchEndDate));
    }

    @Test
    void find_rentals_by_end_date_between() {
        // Given
        LocalDateTime searchStartDate = LocalDateTime.now().plusDays(4);
        LocalDateTime searchEndDate = LocalDateTime.now().plusDays(10);
        
        Rental rental1 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                "account-1",
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(5), // Within range
                "Location A",
                "Location B",
                BigDecimal.valueOf(160.00)
        );

        Rental rental2 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                "account-2",
                LocalDateTime.now().plusDays(8),
                LocalDateTime.now().plusDays(15), // Outside range
                "Location C",
                "Location D",
                BigDecimal.valueOf(420.00)
        );

        rentalRepository.save(rental1);
        rentalRepository.save(rental2);

        // When
        List<Rental> rentalsInRange = rentalRepository.findByEndDateBetween(searchStartDate, searchEndDate);

        // Then
        assertThat(rentalsInRange).hasSize(1);
        assertThat(rentalsInRange.get(0).getEndDate()).isBetween(searchStartDate, searchEndDate);
    }

    @Test
    void find_rentals_by_location() {
        // Given
        String location = "Airport Terminal";

        Rental rental1 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                "account-1",
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(3),
                location, // Matches
                "Downtown",
                BigDecimal.valueOf(80.00)
        );

        Rental rental2 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                "account-2",
                LocalDateTime.now().plusDays(5),
                LocalDateTime.now().plusDays(7),
                "Downtown",
                location, // Matches
                BigDecimal.valueOf(120.00)
        );

        Rental rental3 = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(50.00),
                "article-3",
                "account-3",
                LocalDateTime.now().plusDays(8),
                LocalDateTime.now().plusDays(10),
                "Mall",
                "Hotel", // No match
                BigDecimal.valueOf(100.00)
        );

        rentalRepository.save(rental1);
        rentalRepository.save(rental2);
        rentalRepository.save(rental3);

        // When
        List<Rental> rentalsAtLocation = rentalRepository.findByLocation(location);

        // Then
        assertThat(rentalsAtLocation).hasSize(2);
        assertThat(rentalsAtLocation).anyMatch(r -> r.getPickupLocation().equals(location));
        assertThat(rentalsAtLocation).anyMatch(r -> r.getDropoffLocation().equals(location));
    }

    @Test
    void find_rentals_by_account_id_and_status() {
        // Given
        String accountId = "account-test-456";

        Rental pendingRental = createRental(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(40.00),
                "article-1",
                accountId,
                LocalDateTime.now().plusDays(1),
                LocalDateTime.now().plusDays(3),
                "Location A",
                "Location B",
                BigDecimal.valueOf(80.00)
        );

        Rental confirmedRental = createRental(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                1,
                BigDecimal.valueOf(60.00),
                "article-2",
                accountId,
                LocalDateTime.now().plusDays(5),
                LocalDateTime.now().plusDays(7),
                "Location C",
                "Location D",
                BigDecimal.valueOf(120.00)
        );

        rentalRepository.save(pendingRental);
        rentalRepository.save(confirmedRental);

        // When
        List<Rental> pendingRentalsForAccount = rentalRepository.findByAccountIdAndStatus(accountId, OrderStatus.PENDING);

        // Then
        assertThat(pendingRentalsForAccount).hasSize(1);
        assertThat(pendingRentalsForAccount.get(0).getAccountId()).isEqualTo(accountId);
        assertThat(pendingRentalsForAccount.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
    }
}
