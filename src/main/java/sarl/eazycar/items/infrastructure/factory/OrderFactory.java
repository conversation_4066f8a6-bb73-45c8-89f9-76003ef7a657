package sarl.eazycar.items.infrastructure.factory;

import org.springframework.stereotype.Component;
import sarl.eazycar.items.application.rest.request.OrderRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;

import java.time.LocalDateTime;

/**
 * Factory pour créer des instances d'Order
 */
@Component
public class OrderFactory {

    public Order createOrderFromRequest(OrderRequest request, Article article) {
        Order order;
        
        if ("PURCHASE".equals(request.getOrderType())) {
            Purchase purchase = new Purchase();
            purchase.setShippingAddress(request.getShippingAddress());
            purchase.setEstimatedDeliveryDate(request.getEstimatedDeliveryDate());
            order = purchase;
        } else if ("RENTAL".equals(request.getOrderType())) {
            Rental rental = new Rental();
            rental.setStartDate(request.getStartDate());
            rental.setEndDate(request.getEndDate());
            rental.setPickupLocation(request.getPickupLocation());
            rental.setDropoffLocation(request.getDropoffLocation());
            order = rental;
        } else {
            throw new IllegalArgumentException("Invalid order type: " + request.getOrderType());
        }
        
        // Propriétés communes
        order.setOrderDate(LocalDateTime.now());
        order.setQuantity(request.getQuantity());
        order.setUnitPrice(request.getUnitPrice());
        order.setCurrency(request.getCurrency());
        order.setArticle(article);
        order.setPriceId(request.getPriceId());
        
        return order;
    }
}
