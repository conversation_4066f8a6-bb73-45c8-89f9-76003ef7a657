package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.PaymentDto;
import sarl.eazycar.items.domain.entity.Payment;

/**
 * Mapper pour Payment
 */
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface PaymentMapper {

    PaymentMapper INSTANCE = Mappers.getMapper(PaymentMapper.class);

    @Mapping(target = "invoiceId", source = "invoice.invoiceId")
    PaymentDto toDto(Payment payment);
}
