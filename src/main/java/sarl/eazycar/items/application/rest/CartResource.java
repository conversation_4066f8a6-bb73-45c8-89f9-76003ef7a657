package sarl.eazycar.items.application.rest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.CartDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.rest.api.CartApi;
import sarl.eazycar.items.application.rest.request.OrderRequest;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.ICartService;
import sarl.eazycar.items.domain.service.ICheckoutService;
import sarl.eazycar.items.infrastructure.factory.OrderFactory;
import sarl.eazycar.items.infrastructure.mapper.CartMapper;
import sarl.eazycar.items.infrastructure.mapper.InvoiceMapper;

/**
 * Contrôleur REST pour la gestion des paniers
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class CartResource implements CartApi {

    private final ICartService cartService;
    private final ICheckoutService checkoutService;
    private final IArticleService articleService;
    private final OrderFactory orderFactory;
    private final CartMapper cartMapper;
    private final InvoiceMapper invoiceMapper;

    @Override
    public ResponseEntity<CartDto> getOrCreateCart(String accountId) {
        log.info("Getting or creating cart for account: {}", accountId);

        Cart cart = cartService.getOrCreateActiveCart(accountId);
        CartDto cartDto = cartMapper.toDto(cart);

        return ResponseEntity.ok(cartDto);
    }

    @Override
    public ResponseEntity<CartDto> addOrderToCart(String accountId, OrderRequest orderRequest) {
        log.info("Adding order to cart for account: {}", accountId);

        Order order = createOrderFromRequest(orderRequest);
        Cart cart = cartService.addOrderToCart(accountId, order);
        CartDto cartDto = cartMapper.toDto(cart);

        return ResponseEntity.ok(cartDto);
    }

    @Override
    public ResponseEntity<CartDto> updateOrderQuantity(String accountId, String orderId, Integer quantity) {
        log.info("Updating order quantity for account: {}, order: {}, quantity: {}",
                accountId, orderId, quantity);

        Cart cart = cartService.updateOrderQuantity(accountId, orderId, quantity);
        CartDto cartDto = cartMapper.toDto(cart);

        return ResponseEntity.ok(cartDto);
    }

    @Override
    public ResponseEntity<CartDto> removeOrderFromCart(String accountId, String orderId) {
        log.info("Removing order from cart for account: {}, order: {}", accountId, orderId);

        Cart cart = cartService.removeOrderFromCart(accountId, orderId);
        CartDto cartDto = cartMapper.toDto(cart);

        return ResponseEntity.ok(cartDto);
    }

    @Override
    public ResponseEntity<CartDto> clearCart(String accountId) {
        log.info("Clearing cart for account: {}", accountId);

        Cart cart = cartService.clearCart(accountId);
        CartDto cartDto = cartMapper.toDto(cart);

        return ResponseEntity.ok(cartDto);
    }

    @Override
    public ResponseEntity<InvoiceDto> checkout(String accountId) {
        log.info("Processing checkout for account: {}", accountId);

        Invoice invoice = checkoutService.processCheckout(accountId);
        InvoiceDto invoiceDto = invoiceMapper.toDto(invoice);

        return ResponseEntity.ok(invoiceDto);
    }

    private Order createOrderFromRequest(OrderRequest request) {
        // Récupérer l'article
        var article = articleService.getArticleById(request.getArticleId());

        // Utiliser la factory pour créer l'ordre
        return orderFactory.createOrderFromRequest(request, article);
    }
}
