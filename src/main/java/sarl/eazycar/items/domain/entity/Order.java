package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.*;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

/**
 * Entité abstraite Order - classe de base pour Purchase et Rental/Reservation Utilise la stratégie
 * d'héritage SINGLE_TABLE avec colonne discriminante ORDER_TYPE
 */
@Entity
@Table(name = "ORDERS")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "ORDER_TYPE", discriminatorType = DiscriminatorType.STRING)
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public abstract class Order extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "ORDER_ID")
  private String orderId;

  @Column(name = "ORDER_DATE")
  private LocalDateTime orderDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "STATUS")
  private OrderStatus status;

  @Column(name = "CURRENCY")
  private String currency;

  @Column(name = "CONFIRMATION_DATE")
  private LocalDateTime confirmationDate;

  @Column(name = "QUANTITY")
  private Integer quantity;

  @Column(name = "UNIT_PRICE", precision = 19, scale = 2)
  private BigDecimal unitPrice;

  @ManyToOne
  @JoinColumn(name = "ARTICLE_ID")
  private Article article;

  @ManyToOne
  @JoinColumn(name = "CART_ID", nullable = true)
  private Cart cart;

  @Column(name = "PRICE_ID")
  private String priceId;

  @Column(name = "ACCOUNT_ID")
  private String accountId;
}
