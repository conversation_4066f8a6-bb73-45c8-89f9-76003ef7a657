package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.InvoiceRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour InvoiceService
 */
@ExtendWith(MockitoExtension.class)
class InvoiceServiceTest {

    @Mock
    private InvoiceRepository invoiceRepository;

    @InjectMocks
    private InvoiceService invoiceService;

    private Purchase testOrder;
    private Invoice testInvoice;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        // Créer un article de test
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .available(true)
                .build();

        // Créer une commande de test
        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setArticle(testArticle);
        testOrder.setOrderDate(LocalDateTime.now());
        testOrder.setStatus(OrderStatus.CONFIRMED);
        testOrder.setShippingAddress("123 Test Street");

        // Créer une facture de test
        testInvoice = Invoice.builder()
                .invoiceId("invoice-123")
                .issueDate(LocalDate.now())
                .dueDate(LocalDate.now().plusDays(30))
                .status(InvoiceStatus.DRAFT)
                .order(testOrder)
                .build();
    }

    @Test
    @DisplayName("Should generate invoice for order successfully")
    void shouldGenerateInvoiceForOrderSuccessfully() {
        // Given
        when(invoiceRepository.findByOrderId("order-123")).thenReturn(Optional.empty());
        when(invoiceRepository.save(any(Invoice.class))).thenReturn(testInvoice);

        // When
        Invoice result = invoiceService.generateInvoiceForOrder(testOrder);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getInvoiceId()).isEqualTo("invoice-123");
        assertThat(result.getStatus()).isEqualTo(InvoiceStatus.DRAFT);
        assertThat(result.getOrder()).isEqualTo(testOrder);
        assertThat(result.getIssueDate()).isEqualTo(LocalDate.now());
        assertThat(result.getDueDate()).isEqualTo(LocalDate.now().plusDays(30));
        
        verify(invoiceRepository).save(any(Invoice.class));
    }

    @Test
    @DisplayName("Should throw exception when invoice already exists for order")
    void shouldThrowExceptionWhenInvoiceAlreadyExistsForOrder() {
        // Given
        when(invoiceRepository.findByOrderId("order-123")).thenReturn(Optional.of(testInvoice));

        // When & Then
        assertThatThrownBy(() -> invoiceService.generateInvoiceForOrder(testOrder))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invoice already exists for order: order-123");
        
        verify(invoiceRepository, never()).save(any(Invoice.class));
    }

    @Test
    @DisplayName("Should throw exception when order is null")
    void shouldThrowExceptionWhenOrderIsNull() {
        // When & Then
        assertThatThrownBy(() -> invoiceService.generateInvoiceForOrder(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order cannot be null");
    }

    @Test
    @DisplayName("Should get invoice by order ID successfully")
    void shouldGetInvoiceByOrderIdSuccessfully() {
        // Given
        when(invoiceRepository.findByOrderId("order-123")).thenReturn(Optional.of(testInvoice));

        // When
        Invoice result = invoiceService.getInvoiceByOrderId("order-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getInvoiceId()).isEqualTo("invoice-123");
        assertThat(result.getOrder()).isEqualTo(testOrder);
    }

    @Test
    @DisplayName("Should throw exception when invoice not found by order ID")
    void shouldThrowExceptionWhenInvoiceNotFoundByOrderId() {
        // Given
        when(invoiceRepository.findByOrderId("invalid-order")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> invoiceService.getInvoiceByOrderId("invalid-order"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invoice not found for order: invalid-order");
    }

    @Test
    @DisplayName("Should get invoice by ID successfully")
    void shouldGetInvoiceByIdSuccessfully() {
        // Given
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));

        // When
        Invoice result = invoiceService.getInvoiceById("invoice-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getInvoiceId()).isEqualTo("invoice-123");
    }

    @Test
    @DisplayName("Should throw exception when invoice not found by ID")
    void shouldThrowExceptionWhenInvoiceNotFoundById() {
        // Given
        when(invoiceRepository.findById("invalid-invoice")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> invoiceService.getInvoiceById("invalid-invoice"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invoice not found: invalid-invoice");
    }

    @Test
    @DisplayName("Should update invoice status successfully")
    void shouldUpdateInvoiceStatusSuccessfully() {
        // Given
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));
        when(invoiceRepository.save(any(Invoice.class))).thenReturn(testInvoice);

        // When
        Invoice result = invoiceService.updateInvoiceStatus("invoice-123", InvoiceStatus.SENT);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(InvoiceStatus.SENT);
        verify(invoiceRepository).save(testInvoice);
    }

    @Test
    @DisplayName("Should throw exception for invalid status transition")
    void shouldThrowExceptionForInvalidStatusTransition() {
        // Given
        testInvoice.setStatus(InvoiceStatus.DRAFT);
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));

        // When & Then
        assertThatThrownBy(() -> invoiceService.updateInvoiceStatus("invoice-123", InvoiceStatus.PAID))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from DRAFT to PAID");
    }

    @Test
    @DisplayName("Should throw exception when trying to change paid invoice status")
    void shouldThrowExceptionWhenTryingToChangePaidInvoiceStatus() {
        // Given
        testInvoice.setStatus(InvoiceStatus.PAID);
        when(invoiceRepository.findById("invoice-123")).thenReturn(Optional.of(testInvoice));

        // When & Then
        assertThatThrownBy(() -> invoiceService.updateInvoiceStatus("invoice-123", InvoiceStatus.OVERDUE))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cannot change status of a paid invoice");
    }

    @Test
    @DisplayName("Should get invoices by status successfully")
    void shouldGetInvoicesByStatusSuccessfully() {
        // Given
        List<Invoice> invoices = List.of(testInvoice);
        when(invoiceRepository.findByStatus(InvoiceStatus.DRAFT)).thenReturn(invoices);

        // When
        List<Invoice> result = invoiceService.getInvoicesByStatus(InvoiceStatus.DRAFT);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getInvoiceId()).isEqualTo("invoice-123");
    }

    @Test
    @DisplayName("Should get overdue invoices successfully")
    void shouldGetOverdueInvoicesSuccessfully() {
        // Given
        List<Invoice> overdueInvoices = List.of(testInvoice);
        when(invoiceRepository.findOverdueInvoices(any(LocalDate.class))).thenReturn(overdueInvoices);

        // When
        List<Invoice> result = invoiceService.getOverdueInvoices();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        verify(invoiceRepository).findOverdueInvoices(any(LocalDate.class));
    }

    @Test
    @DisplayName("Should throw exception when status is null")
    void shouldThrowExceptionWhenStatusIsNull() {
        // When & Then
        assertThatThrownBy(() -> invoiceService.getInvoicesByStatus(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }
}
