package sarl.eazycar.items.infrastructure.factory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.rest.request.OrderRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;

/** Tests unitaires pour OrderFactory */
@ExtendWith(MockitoExtension.class)
class OrderFactoryImplTest {

  @InjectMocks private OrderFactory orderFactory;

  private Article testArticle;
  private OrderRequest purchaseRequest;
  private OrderRequest rentalRequest;

  @BeforeEach
  void setUp() {
    testArticle =
        Article.builder()
            .articleId("article-123")
            .name("Test Car")
            .description("Test Description")
            .available(true)
            .build();

    purchaseRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(25000.00))
            .currency("EUR")
            .orderType("PURCHASE")
            .shippingAddress("123 Test Street, Test City")
            .estimatedDeliveryDate(LocalDate.now().plusDays(7))
            .build();

    rentalRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-456")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType("RENTAL")
            .startDate(LocalDateTime.now().plusDays(1))
            .endDate(LocalDateTime.now().plusDays(7))
            .pickupLocation("Pickup Location")
            .dropoffLocation("Dropoff Location")
            .build();
  }

  @Test
  @DisplayName("Should create Purchase order from request successfully")
  void shouldCreatePurchaseOrderFromRequestSuccessfully() {
    // When
    Order result = orderFactory.buildOrder(purchaseRequest, testArticle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result).isInstanceOf(Purchase.class);

    Purchase purchase = (Purchase) result;

    // Vérifier les propriétés communes
    assertThat(purchase.getQuantity()).isEqualTo(1);
    assertThat(purchase.getUnitPrice()).isEqualTo(BigDecimal.valueOf(25000.00));
    assertThat(purchase.getCurrency()).isEqualTo("EUR");
    assertThat(purchase.getArticle()).isEqualTo(testArticle);
    assertThat(purchase.getPriceId()).isEqualTo("price-123");
    assertThat(purchase.getOrderDate()).isNotNull();

    // Vérifier les propriétés spécifiques à Purchase
    assertThat(purchase.getShippingAddress()).isEqualTo("123 Test Street, Test City");
    assertThat(purchase.getEstimatedDeliveryDate()).isEqualTo(LocalDate.now().plusDays(7));
  }

  @Test
  @DisplayName("Should create Rental order from request successfully")
  void shouldCreateRentalOrderFromRequestSuccessfully() {
    // When
    Order result = orderFactory.buildOrder(rentalRequest, testArticle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result).isInstanceOf(Rental.class);

    Rental rental = (Rental) result;

    // Vérifier les propriétés communes
    assertThat(rental.getQuantity()).isEqualTo(1);
    assertThat(rental.getUnitPrice()).isEqualTo(BigDecimal.valueOf(100.00));
    assertThat(rental.getCurrency()).isEqualTo("EUR");
    assertThat(rental.getArticle()).isEqualTo(testArticle);
    assertThat(rental.getPriceId()).isEqualTo("price-456");
    assertThat(rental.getOrderDate()).isNotNull();

    // Vérifier les propriétés spécifiques à Rental
    assertThat(rental.getStartDate()).isEqualTo(rentalRequest.getStartDate());
    assertThat(rental.getEndDate()).isEqualTo(rentalRequest.getEndDate());
    assertThat(rental.getPickupLocation()).isEqualTo("Pickup Location");
    assertThat(rental.getDropoffLocation()).isEqualTo("Dropoff Location");
  }

  @Test
  @DisplayName("Should throw exception for invalid order type")
  void shouldThrowExceptionForInvalidOrderType() {
    // Given
    OrderRequest invalidRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType("INVALID_TYPE")
            .build();

    // When & Then
    assertThatThrownBy(() -> orderFactory.buildOrder(invalidRequest, testArticle))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid order type: INVALID_TYPE");
  }

  @Test
  @DisplayName("Should handle null order type")
  void shouldHandleNullOrderType() {
    // Given
    OrderRequest nullTypeRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType(null)
            .build();

    // When & Then
    assertThatThrownBy(() -> orderFactory.buildOrder(nullTypeRequest, testArticle))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid order type: null");
  }

  @Test
  @DisplayName("Should handle empty order type")
  void shouldHandleEmptyOrderType() {
    // Given
    OrderRequest emptyTypeRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType("")
            .build();

    // When & Then
    assertThatThrownBy(() -> orderFactory.buildOrder(emptyTypeRequest, testArticle))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid order type: ");
  }

  @Test
  @DisplayName("Should create Purchase with minimal required fields")
  void shouldCreatePurchaseWithMinimalRequiredFields() {
    // Given
    OrderRequest minimalPurchaseRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(2)
            .unitPrice(BigDecimal.valueOf(15000.00))
            .currency("USD")
            .orderType("PURCHASE")
            // Pas d'adresse de livraison ni de date de livraison
            .build();

    // When
    Order result = orderFactory.buildOrder(minimalPurchaseRequest, testArticle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result).isInstanceOf(Purchase.class);

    Purchase purchase = (Purchase) result;
    assertThat(purchase.getQuantity()).isEqualTo(2);
    assertThat(purchase.getUnitPrice()).isEqualTo(BigDecimal.valueOf(15000.00));
    assertThat(purchase.getCurrency()).isEqualTo("USD");
    assertThat(purchase.getShippingAddress()).isNull();
    assertThat(purchase.getEstimatedDeliveryDate()).isNull();
  }

  @Test
  @DisplayName("Should create Rental with minimal required fields")
  void shouldCreateRentalWithMinimalRequiredFields() {
    // Given
    OrderRequest minimalRentalRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-456")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(75.00))
            .currency("EUR")
            .orderType("RENTAL")
            .startDate(LocalDateTime.now().plusDays(2))
            .endDate(LocalDateTime.now().plusDays(5))
            // Pas de lieux de prise en charge et de retour
            .build();

    // When
    Order result = orderFactory.buildOrder(minimalRentalRequest, testArticle);

    // Then
    assertThat(result).isNotNull();
    assertThat(result).isInstanceOf(Rental.class);

    Rental rental = (Rental) result;
    assertThat(rental.getQuantity()).isEqualTo(1);
    assertThat(rental.getUnitPrice()).isEqualTo(BigDecimal.valueOf(75.00));
    assertThat(rental.getCurrency()).isEqualTo("EUR");
    assertThat(rental.getPickupLocation()).isNull();
    assertThat(rental.getDropoffLocation()).isNull();
    assertThat(rental.getStartDate()).isNotNull();
    assertThat(rental.getEndDate()).isNotNull();
  }

  @Test
  @DisplayName("Should set order date to current time")
  void shouldSetOrderDateToCurrentTime() {
    // Given
    LocalDateTime beforeCreation = LocalDateTime.now().minusSeconds(1);

    // When
    Order result = orderFactory.buildOrder(purchaseRequest, testArticle);

    // Then
    LocalDateTime afterCreation = LocalDateTime.now().plusSeconds(1);
    assertThat(result.getOrderDate()).isAfter(beforeCreation);
    assertThat(result.getOrderDate()).isBefore(afterCreation);
  }

  @Test
  @DisplayName("Should handle case insensitive order type")
  void shouldHandleCaseInsensitiveOrderType() {
    // Given
    OrderRequest lowerCaseRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType("purchase") // lowercase
            .build();

    // When & Then
    // Note: La factory actuelle est case-sensitive, donc cela devrait lever une exception
    assertThatThrownBy(() -> orderFactory.buildOrder(lowerCaseRequest, testArticle))
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessage("Invalid order type: purchase");
  }

  @Test
  @DisplayName("Should preserve all article properties")
  void shouldPreserveAllArticleProperties() {
    // Given
    Article detailedArticle =
        Article.builder()
            .articleId("detailed-article-123")
            .name("Detailed Test Car")
            .description("Very detailed description")
            .available(true)
            .cityLocation("Test City")
            .districtLocation("Test District")
            .build();

    // When
    Order result = orderFactory.buildOrder(purchaseRequest, detailedArticle);

    // Then
    assertThat(result.getArticle()).isEqualTo(detailedArticle);
    assertThat(result.getArticle().getArticleId()).isEqualTo("detailed-article-123");
    assertThat(result.getArticle().getName()).isEqualTo("Detailed Test Car");
    assertThat(result.getArticle().getDescription()).isEqualTo("Very detailed description");
    assertThat(result.getArticle().getCityLocation()).isEqualTo("Test City");
    assertThat(result.getArticle().getDistrictLocation()).isEqualTo("Test District");
  }

  @Test
  @DisplayName("Should handle zero quantity")
  void shouldHandleZeroQuantity() {
    // Given
    OrderRequest zeroQuantityRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(0)
            .unitPrice(BigDecimal.valueOf(100.00))
            .currency("EUR")
            .orderType("PURCHASE")
            .build();

    // When
    Order result = orderFactory.buildOrder(zeroQuantityRequest, testArticle);

    // Then
    assertThat(result.getQuantity()).isEqualTo(0);
  }

  @Test
  @DisplayName("Should handle zero unit price")
  void shouldHandleZeroUnitPrice() {
    // Given
    OrderRequest zeroPriceRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1)
            .unitPrice(BigDecimal.ZERO)
            .currency("EUR")
            .orderType("PURCHASE")
            .build();

    // When
    Order result = orderFactory.buildOrder(zeroPriceRequest, testArticle);

    // Then
    assertThat(result.getUnitPrice()).isEqualTo(BigDecimal.ZERO);
  }

  @Test
  @DisplayName("Should handle large quantities and prices")
  void shouldHandleLargeQuantitiesAndPrices() {
    // Given
    OrderRequest largeValuesRequest =
        OrderRequest.builder()
            .articleId("article-123")
            .priceId("price-123")
            .quantity(1000)
            .unitPrice(new BigDecimal("999999.99"))
            .currency("EUR")
            .orderType("PURCHASE")
            .build();

    // When
    Order result = orderFactory.buildOrder(largeValuesRequest, testArticle);

    // Then
    assertThat(result.getQuantity()).isEqualTo(1000);
    assertThat(result.getUnitPrice()).isEqualTo(new BigDecimal("999999.99"));
  }
}
