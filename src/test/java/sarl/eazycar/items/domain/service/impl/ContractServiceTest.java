package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.ContractRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour ContractService
 */
@ExtendWith(MockitoExtension.class)
class ContractServiceTest {

    @Mock
    private ContractRepository contractRepository;

    @InjectMocks
    private ContractService contractService;

    private Purchase testOrder;
    private Contract testContract;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .description("Test Description")
                .available(true)
                .build();

        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setAccountId("account-123");
        testOrder.setStatus(OrderStatus.PENDING);
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setCurrency("EUR");
        testOrder.setArticle(testArticle);
        testOrder.setShippingAddress("123 Test Street");

        testContract = Contract.builder()
                .contractId("contract-123")
                .documentUrl("https://example.com/contract.pdf")
                .status(ContractStatus.PENDING_SIGNATURE)
                .order(testOrder)
                .build();
    }

    @Test
    @DisplayName("Should generate contract for order successfully")
    void shouldGenerateContractForOrderSuccessfully() {
        // Given
        when(contractRepository.save(any(Contract.class))).thenReturn(testContract);

        // When
        Contract result = contractService.generateContractForOrder(testOrder);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrder().getOrderId()).isEqualTo("order-123");
        assertThat(result.getStatus()).isEqualTo(ContractStatus.PENDING_SIGNATURE);
        assertThat(result.getDocumentUrl()).isNotNull();
        verify(contractRepository).save(any(Contract.class));
    }

    @Test
    @DisplayName("Should throw exception when order is null")
    void shouldThrowExceptionWhenOrderIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.generateContractForOrder(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order cannot be null");
    }

    @Test
    @DisplayName("Should throw exception when order has no article")
    void shouldThrowExceptionWhenOrderHasNoArticle() {
        // Given
        testOrder.setArticle(null);

        // When & Then
        assertThatThrownBy(() -> contractService.generateContractForOrder(testOrder))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order must have an associated article");
    }

    @Test
    @DisplayName("Should get contract by order ID successfully")
    void shouldGetContractByOrderIdSuccessfully() {
        // Given
        when(contractRepository.findByOrderOrderId("order-123")).thenReturn(Optional.of(testContract));

        // When
        Contract result = contractService.getContractByOrderId("order-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrder().getOrderId()).isEqualTo("order-123");
        verify(contractRepository).findByOrderOrderId("order-123");
    }

    @Test
    @DisplayName("Should throw exception when contract not found by order ID")
    void shouldThrowExceptionWhenContractNotFoundByOrderId() {
        // Given
        when(contractRepository.findByOrderOrderId("order-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> contractService.getContractByOrderId("order-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract not found for order: order-123");
    }

    @Test
    @DisplayName("Should throw exception when order ID is null")
    void shouldThrowExceptionWhenOrderIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractByOrderId(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should get contract by ID successfully")
    void shouldGetContractByIdSuccessfully() {
        // Given
        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));

        // When
        Contract result = contractService.getContractById("contract-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContractId()).isEqualTo("contract-123");
        verify(contractRepository).findById("contract-123");
    }

    @Test
    @DisplayName("Should throw exception when contract not found by ID")
    void shouldThrowExceptionWhenContractNotFoundById() {
        // Given
        when(contractRepository.findById("contract-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> contractService.getContractById("contract-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract not found: contract-123");
    }

    @Test
    @DisplayName("Should sign contract successfully")
    void shouldSignContractSuccessfully() {
        // Given
        LocalDateTime signatureDate = LocalDateTime.now();
        Contract signedContract = Contract.builder()
                .contractId("contract-123")
                .documentUrl("https://example.com/contract.pdf")
                .status(ContractStatus.SIGNED)
                .signatureDate(signatureDate)
                .order(testOrder)
                .build();

        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));
        when(contractRepository.save(any(Contract.class))).thenReturn(signedContract);

        // When
        Contract result = contractService.signContract("contract-123", signatureDate);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(ContractStatus.SIGNED);
        assertThat(result.getSignatureDate()).isEqualTo(signatureDate);
        verify(contractRepository).findById("contract-123");
        verify(contractRepository).save(any(Contract.class));
    }

    @Test
    @DisplayName("Should throw exception when trying to sign already signed contract")
    void shouldThrowExceptionWhenTryingToSignAlreadySignedContract() {
        // Given
        testContract.setStatus(ContractStatus.SIGNED);
        testContract.setSignatureDate(LocalDateTime.now());
        when(contractRepository.findById("contract-123")).thenReturn(Optional.of(testContract));

        // When & Then
        assertThatThrownBy(() -> contractService.signContract("contract-123", LocalDateTime.now()))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Contract is not in PENDING_SIGNATURE status. Current status: SIGNED");
    }

    @Test
    @DisplayName("Should get contracts by status successfully")
    void shouldGetContractsByStatusSuccessfully() {
        // Given
        List<Contract> contracts = List.of(testContract);
        when(contractRepository.findByStatus(ContractStatus.PENDING_SIGNATURE)).thenReturn(contracts);

        // When
        List<Contract> result = contractService.getContractsByStatus(ContractStatus.PENDING_SIGNATURE);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getStatus()).isEqualTo(ContractStatus.PENDING_SIGNATURE);
        verify(contractRepository).findByStatus(ContractStatus.PENDING_SIGNATURE);
    }

    @Test
    @DisplayName("Should return empty list when no contracts found by status")
    void shouldReturnEmptyListWhenNoContractsFoundByStatus() {
        // Given
        when(contractRepository.findByStatus(ContractStatus.SIGNED)).thenReturn(List.of());

        // When
        List<Contract> result = contractService.getContractsByStatus(ContractStatus.SIGNED);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        verify(contractRepository).findByStatus(ContractStatus.SIGNED);
    }

    @Test
    @DisplayName("Should throw exception when status is null")
    void shouldThrowExceptionWhenStatusIsNull() {
        // When & Then
        assertThatThrownBy(() -> contractService.getContractsByStatus(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }
}
