package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.CartRepository;
import sarl.eazycar.items.domain.repository.OrderRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour CartService
 */
@ExtendWith(MockitoExtension.class)
class CartServiceTest {

    @Mock
    private CartRepository cartRepository;

    @Mock
    private OrderRepository orderRepository;

    @InjectMocks
    private CartService cartService;

    private Cart activeCart;
    private Purchase testOrder;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        // Créer un article de test
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .available(true)
                .build();

        // Créer une commande de test
        testOrder = new Purchase();
        testOrder.setOrderId("order-123");
        testOrder.setQuantity(1);
        testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
        testOrder.setArticle(testArticle);
        testOrder.setOrderDate(LocalDateTime.now());
        testOrder.setStatus(OrderStatus.IN_CART);

        // Créer un panier actif de test
        activeCart = Cart.builder()
                .cartId("cart-123")
                .accountId("account-123")
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.ZERO)
                .orders(new ArrayList<>())
                .build();
    }

    @Test
    @DisplayName("Should get existing active cart for user")
    void shouldGetExistingActiveCart() {
        // Given
        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));

        // When
        Cart result = cartService.getOrCreateActiveCart("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getCartId()).isEqualTo("cart-123");
        assertThat(result.getStatus()).isEqualTo(CartStatus.ACTIVE);
        verify(cartRepository, never()).save(any(Cart.class));
    }

    @Test
    @DisplayName("Should create new cart when none exists")
    void shouldCreateNewCartWhenNoneExists() {
        // Given
        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of());
        when(cartRepository.save(any(Cart.class))).thenReturn(activeCart);

        // When
        Cart result = cartService.getOrCreateActiveCart("account-123");

        // Then
        assertThat(result).isNotNull();
        verify(cartRepository).save(any(Cart.class));
    }

    @Test
    @DisplayName("Should throw exception when account ID is null")
    void shouldThrowExceptionWhenAccountIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> cartService.getOrCreateActiveCart(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Account ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should add order to cart successfully")
    void shouldAddOrderToCartSuccessfully() {
        // Given
        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testOrder);
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenReturn(activeCart);

        // When
        Cart result = cartService.addOrderToCart("account-123", testOrder);

        // Then
        assertThat(result).isNotNull();
        verify(orderRepository).save(testOrder);
        assertThat(testOrder.getAccountId()).isEqualTo("account-123");
        assertThat(testOrder.getStatus()).isEqualTo(OrderStatus.IN_CART);
        assertThat(testOrder.getCart()).isEqualTo(activeCart);
    }

    @Test
    @DisplayName("Should update order quantity successfully")
    void shouldUpdateOrderQuantitySuccessfully() {
        // Given
        testOrder.setCart(activeCart);
        activeCart.getOrders().add(testOrder);

        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testOrder));
        when(orderRepository.save(any(Purchase.class))).thenReturn(testOrder);
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenReturn(activeCart);

        // When
        Cart result = cartService.updateOrderQuantity("account-123", "order-123", 3);

        // Then
        assertThat(result).isNotNull();
        assertThat(testOrder.getQuantity()).isEqualTo(3);
        verify(orderRepository).save(testOrder);
    }

    @Test
    @DisplayName("Should throw exception when updating quantity with invalid value")
    void shouldThrowExceptionWhenUpdatingQuantityWithInvalidValue() {
        // When & Then
        assertThatThrownBy(() -> cartService.updateOrderQuantity("account-123", "order-123", 0))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Quantity must be greater than 0");
    }

    @Test
    @DisplayName("Should remove order from cart successfully")
    void shouldRemoveOrderFromCartSuccessfully() {
        // Given
        testOrder.setCart(activeCart);
        activeCart.getOrders().add(testOrder);

        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testOrder));
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenReturn(activeCart);

        // When
        Cart result = cartService.removeOrderFromCart("account-123", "order-123");

        // Then
        assertThat(result).isNotNull();
        verify(orderRepository).delete(testOrder);
    }

    @Test
    @DisplayName("Should clear cart successfully")
    void shouldClearCartSuccessfully() {
        // Given
        activeCart.getOrders().add(testOrder);

        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenReturn(activeCart);

        // When
        Cart result = cartService.clearCart("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalAmount()).isEqualTo(BigDecimal.ZERO);
        verify(orderRepository).deleteAll(activeCart.getOrders());
    }

    @Test
    @DisplayName("Should calculate cart total correctly")
    void shouldCalculateCartTotalCorrectly() {
        // Given
        Purchase order1 = new Purchase();
        order1.setQuantity(2);
        order1.setUnitPrice(BigDecimal.valueOf(50.00));

        Purchase order2 = new Purchase();
        order2.setQuantity(1);
        order2.setUnitPrice(BigDecimal.valueOf(30.00));

        activeCart.getOrders().add(order1);
        activeCart.getOrders().add(order2);

        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        Cart result = cartService.calculateCartTotal("cart-123");

        // Then
        assertThat(result.getTotalAmount()).isEqualTo(BigDecimal.valueOf(130.00)); // (2*50) + (1*30)
    }

    @Test
    @DisplayName("Should throw exception when cart not found")
    void shouldThrowExceptionWhenCartNotFound() {
        // Given
        when(cartRepository.findById("invalid-cart")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> cartService.getCartById("invalid-cart"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart not found: invalid-cart");
    }

    @Test
    @DisplayName("Should get cart with orders successfully")
    void shouldGetCartWithOrdersSuccessfully() {
        // Given
        activeCart.getOrders().add(testOrder);
        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));

        // When
        Cart result = cartService.getCartWithOrders("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrders()).hasSize(1);
        assertThat(result.getOrders().get(0)).isEqualTo(testOrder);
    }

    @Test
    @DisplayName("Should throw exception when order not found in cart")
    void shouldThrowExceptionWhenOrderNotFoundInCart() {
        // Given
        Purchase orderNotInCart = new Purchase();
        orderNotInCart.setOrderId("order-456");
        orderNotInCart.setQuantity(1);
        orderNotInCart.setUnitPrice(BigDecimal.valueOf(50.00));
        orderNotInCart.setArticle(testArticle);
        orderNotInCart.setOrderDate(LocalDateTime.now());
        orderNotInCart.setStatus(OrderStatus.IN_CART);
        // Note: orderNotInCart n'a pas de cart associé

        when(cartRepository.findByAccountIdAndStatus("account-123", CartStatus.ACTIVE))
                .thenReturn(List.of(activeCart));
        when(orderRepository.findById("order-456")).thenReturn(Optional.of(orderNotInCart));

        // When & Then
        assertThatThrownBy(() -> cartService.updateOrderQuantity("account-123", "order-456", 2))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order not found in user's active cart");
    }

    @Test
    @DisplayName("Should throw exception when order is null")
    void shouldThrowExceptionWhenOrderIsNull() {
        // When & Then
        assertThatThrownBy(() -> cartService.addOrderToCart("account-123", null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order cannot be null");
    }



    @Test
    @DisplayName("Should throw exception when cart ID is null")
    void shouldThrowExceptionWhenCartIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> cartService.getCartById(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when cart ID is empty")
    void shouldThrowExceptionWhenCartIdIsEmpty() {
        // When & Then
        assertThatThrownBy(() -> cartService.getCartById(""))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should handle empty cart calculation")
    void shouldHandleEmptyCartCalculation() {
        // Given
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(activeCart));
        when(cartRepository.save(any(Cart.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        Cart result = cartService.calculateCartTotal("cart-123");

        // Then
        assertThat(result.getTotalAmount()).isEqualTo(BigDecimal.ZERO);
    }
}
